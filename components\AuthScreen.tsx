import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';

export const AuthScreen = () => {
    const [isLoginView, setIsLoginView] = useState(true);
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const { login, signup } = useAuth();

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError('');
        setLoading(true);

        try {
            if (isLoginView) {
                await login(email, password);
            } else {
                await signup(email, password);
            }
        } catch (err: any) {
            setError(err.message.replace('Firebase: ', ''));
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="min-h-screen bg-black flex flex-col justify-center items-center p-4 font-sans">
            <div className="w-full max-w-sm">
                <div className="flex justify-center items-center mb-6">
                    <img src="https://img.icons8.com/plasticine/100/bank.png" alt="FinScan AI Logo" className="w-16 h-16"/>
                    <h1 className="text-3xl font-bold text-gray-100 ml-3">FinScan AI</h1>
                </div>

                <div className="bg-gray-900 border border-gray-800 rounded-2xl p-6">
                    <div className="grid grid-cols-2 gap-2 mb-6 bg-gray-800 p-1 rounded-lg">
                        <button onClick={() => setIsLoginView(true)} className={`py-2 px-4 rounded-md text-sm font-semibold transition-colors ${isLoginView ? 'bg-gray-700 shadow text-white' : 'text-gray-400'}`}>
                            Login
                        </button>
                        <button onClick={() => setIsLoginView(false)} className={`py-2 px-4 rounded-md text-sm font-semibold transition-colors ${!isLoginView ? 'bg-gray-700 shadow text-white' : 'text-gray-400'}`}>
                            Sign Up
                        </button>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                            <label className="text-sm font-medium text-gray-400">Email</label>
                            <input
                                type="email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                required
                                className="mt-1 w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white p-3"
                            />
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-400">Password</label>
                            <input
                                type="password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                required
                                className="mt-1 w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white p-3"
                            />
                        </div>
                        
                        {error && <p className="text-red-400 text-sm text-center">{error}</p>}

                        <button type="submit" disabled={loading} className="w-full bg-indigo-600 text-white font-semibold py-3 px-4 rounded-lg hover:bg-indigo-500 disabled:bg-indigo-400/50 disabled:cursor-not-allowed flex items-center justify-center transition-colors">
                            {loading ? (
                                <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                            ) : (isLoginView ? 'Login' : 'Create Account')}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    );
};
