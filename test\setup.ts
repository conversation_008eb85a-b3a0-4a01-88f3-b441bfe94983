import '@testing-library/jest-dom';
import { vi } from 'vitest';
import React from 'react';

// Mock Firebase
vi.mock('../firebase', () => ({
  db: {},
  storage: {},
}));

// Mock Firebase Auth
vi.mock('../contexts/AuthContext', () => ({
  useAuth: () => ({
    user: { uid: 'test-user', email: '<EMAIL>' },
    logout: vi.fn(),
  }),
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock Gemini Service
vi.mock('../services/geminiService', () => ({
  scanReceiptWithFirebase: vi.fn(),
}));
