rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Users can read and update their own user document, but not create/delete it.
    match /users/{userId} {
      allow read, update: if request.auth.uid == userId;
    }
    
    // A user can perform any operation on documents in their own subcollections.
    // This single rule covers accounts, transactions, and subscriptions.
    match /users/{userId}/{collection}/{docId} {
      allow read, write, delete: if request.auth.uid == userId;
    }
    
  }
}