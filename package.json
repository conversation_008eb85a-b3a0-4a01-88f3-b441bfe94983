{"name": "finscan-ai", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .ts,.tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "test": "vitest --config vite.config.test.ts", "test:run": "vitest run --config vite.config.test.ts", "test:coverage": "vitest run --coverage --config vite.config.test.ts", "prepare": "husky install"}, "dependencies": {"@google/genai": "^1.17.0", "@types/joi": "^17.2.2", "firebase": "^12.2.1", "firebase-admin": "^13.5.0", "firebase-functions": "^6.4.0", "joi": "^18.0.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0"}, "devDependencies": {"@eslint/js": "^9.35.0", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.14.0", "@typescript-eslint/eslint-plugin": "^8.42.0", "@typescript-eslint/parser": "^8.42.0", "eslint": "^9.35.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^16.1.6", "prettier": "^3.6.2", "typescript": "~5.8.2", "vite": "^6.2.0", "vitest": "^3.2.4"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{js,jsx,json,css,md}": ["prettier --write"]}}