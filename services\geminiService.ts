import { getFunctions, httpsCallable } from 'firebase/functions';
import { getStorage, ref, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import { app } from '../firebase';
import { GeminiScanResult } from '../types';

const functions = getFunctions(app);
const storage = getStorage(app);

// Get a reference to the Cloud Function
const scanReceiptFunction = httpsCallable(functions, 'scanReceipt');

/**
 * Uploads an image to Firebase Storage and calls a Cloud Function to scan it.
 * @param imageFile The image file to upload.
 * @param userId The current user's ID.
 * @param onProgress A callback function to report upload progress.
 * @returns A promise that resolves with the scan result.
 */
export const scanReceiptWithFirebase = async (
    imageFile: File, 
    userId: string, 
    onProgress: (progress: number) => void
): Promise<GeminiScanResult> => {
  if (!userId) {
    throw new Error("User is not authenticated.");
  }

  // 1. Upload the file to Firebase Storage
  const filePath = `receipts/${userId}/${new Date().toISOString()}-${imageFile.name}`;
  const storageRef = ref(storage, filePath);
  const uploadTask = uploadBytesResumable(storageRef, imageFile);

  // Return a new promise that wraps the entire process
  return new Promise((resolve, reject) => {
    uploadTask.on('state_changed', 
      (snapshot) => {
        // Observe state change events such as progress, pause, and resume
        const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
        onProgress(progress);
      }, 
      (error) => {
        // Handle unsuccessful uploads
        console.error("Upload failed:", error);
        reject(new Error("Failed to upload receipt image."));
      }, 
      async () => {
        // Handle successful uploads on complete
        try {
          // 2. Once uploaded, call the Cloud Function with the file path
          onProgress(100); // Ensure progress shows 100%
          console.log("File uploaded, calling cloud function with path:", filePath);
          const result = await scanReceiptFunction({ filePath });

          // The callable function returns a data object
          resolve(result.data as GeminiScanResult);
          
        } catch (error) {
          console.error('Error calling cloud function:', error);
          reject(new Error("Failed to analyze receipt. The AI model might be unavailable."));
        }
      }
    );
  });
};
