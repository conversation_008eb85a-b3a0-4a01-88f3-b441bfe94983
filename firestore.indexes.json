{"indexes": [{"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "subscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "startDate", "order": "ASCENDING"}]}], "fieldOverrides": []}