import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { GoogleGenAI, Type } from "@google/genai";
import { EXPENSE_CATEGORIES } from "./constants";

admin.initializeApp();

const bucket = admin.storage().bucket();

// IMPORTANT: Set your Gemini API key in your Firebase project's environment variables
// using the command: firebase functions:config:set gemini.key="YOUR_API_KEY"
const geminiApiKey = functions.config().gemini.key;

if (!geminiApiKey) {
  console.error(
    "Gemini API key not found. Please set it using `firebase functions:config:set gemini.key='YOUR_API_KEY'`"
  );
}

const ai = new GoogleGenAI({ apiKey: geminiApiKey });

export const scanReceipt = functions.https.onCall(async (data, context) => {
  // 1. Authentication Check
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "The function must be called while authenticated."
    );
  }

  const { filePath } = data;
  if (!filePath) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "The function must be called with a 'filePath' argument."
    );
  }
  
  // Security: Ensure user can only access their own files
  const userId = context.auth.uid;
  if (!filePath.startsWith(`receipts/${userId}/`)) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "You do not have permission to access this file."
      );
  }

  try {
    // 2. Download Image from Cloud Storage
    const file = bucket.file(filePath);
    const [fileBuffer] = await file.download();
    const fileMetadata = await file.getMetadata();
    const mimeType = fileMetadata[0].contentType || "application/octet-stream";

    // 3. Call Gemini API
    const response = await ai.models.generateContent({
      model: "gemini-2.5-flash",
      contents: {
        parts: [
          {
            inlineData: {
              data: fileBuffer.toString("base64"),
              mimeType: mimeType,
            },
          },
          {
            text: `You are an expert receipt analyzer. Analyze this receipt image and extract the merchant name, total amount, and date of the transaction. Based on the merchant and items, suggest an appropriate expense category from the following list: ${EXPENSE_CATEGORIES.join(
              ", "
            )}. Provide the output in a structured JSON format. For the date, use YYYY-MM-DD format.`,
          },
        ],
      },
      config: {
        responseMimeType: "application/json",
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            merchantName: {
              type: Type.STRING,
              description: "The name of the merchant or store.",
            },
            totalAmount: {
              type: Type.NUMBER,
              description:
                "The final total amount of the transaction.",
            },
            transactionDate: {
              type: Type.STRING,
              description:
                "The date of the transaction in YYYY-MM-DD format.",
            },
            category: {
              type: Type.STRING,
              description: "A suggested category from the provided list.",
              enum: EXPENSE_CATEGORIES,
            },
          },
          required: [
            "merchantName",
            "totalAmount",
            "transactionDate",
            "category",
          ],
        },
      },
    });

    const jsonString = response.text.trim();
    return JSON.parse(jsonString);

  } catch (error) {
    console.error("Error processing receipt:", error);
    throw new functions.https.HttpsError(
      "internal",
      "An error occurred while analyzing the receipt."
    );
  }
});
