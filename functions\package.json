{"name": "functions", "description": "Cloud Functions for FinScan AI", "scripts": {"build": "tsc", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"@google/genai": "^1.17.0", "firebase-admin": "^12.0.0", "firebase-functions": "^5.0.0"}, "devDependencies": {"typescript": "^5.0.0", "firebase-functions-test": "^3.1.0"}, "private": true}