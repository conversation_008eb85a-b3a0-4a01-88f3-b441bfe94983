rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // Users can only access their own accounts
      match /accounts/{accountId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      // Users can only access their own transactions
      match /transactions/{transactionId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;

        // Validate transaction data structure
        allow create, update: if request.auth != null
          && request.auth.uid == userId
          && request.resource.data.keys().hasAll(['accountId', 'type', 'amount', 'category', 'description', 'date'])
          && request.resource.data.amount is number
          && request.resource.data.amount > 0
          && request.resource.data.type in ['Income', 'Expense']
          && request.resource.data.accountId is string
          && request.resource.data.category is string
          && request.resource.data.description is string
          && request.resource.data.date is string;
      }

      // Users can only access their own subscriptions
      match /subscriptions/{subscriptionId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;

        // Validate subscription data structure
        allow create, update: if request.auth != null
          && request.auth.uid == userId
          && request.resource.data.keys().hasAll(['accountId', 'amount', 'category', 'description', 'frequency', 'startDate'])
          && request.resource.data.amount is number
          && request.resource.data.amount > 0
          && request.resource.data.frequency in ['Weekly', 'Monthly']
          && request.resource.data.accountId is string
          && request.resource.data.category is string
          && request.resource.data.description is string
          && request.resource.data.startDate is string;
      }
    }

    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}