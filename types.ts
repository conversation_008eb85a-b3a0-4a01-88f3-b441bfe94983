export enum AccountType {
  Checking = 'Checking',
  Savings = 'Savings',
  CreditCard = 'Credit Card',
  Cash = 'Cash',
}

export interface Account {
  id: string;
  name: string;
  type: AccountType;
  initialBalance: number;
}

export enum TransactionType {
  Expense = 'Expense',
  Income = 'Income',
}

export interface Transaction {
  id: string;
  accountId: string;
  type: TransactionType;
  amount: number;
  category: string;
  description: string;
  date: string; // ISO 8601 format
}

export enum SubscriptionFrequency {
  Weekly = 'Weekly',
  Monthly = 'Monthly',
}

export interface Subscription {
  id: string;
  accountId: string;
  amount: number;
  category: string;
  description: string;
  frequency: SubscriptionFrequency;
  startDate: string; // ISO 8601 YYYY-MM-DD for next payment date
}

export interface GeminiScanResult {
  merchantName: string;
  totalAmount: number;
  transactionDate: string; // YYYY-MM-DD
  category: string;
}

export enum Currency {
  USD = 'USD',
  EUR = 'EUR',
  GBP = 'GBP',
}