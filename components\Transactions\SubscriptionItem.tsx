import React from 'react';
import { Subscription, Account } from '../../types';
import { RepeatIcon } from '../Icons';

interface SubscriptionItemProps {
  subscription: Subscription;
  account?: Account;
  onClick: () => void;
  formatCurrency: (amount: number) => string;
}

export const SubscriptionItem: React.FC<SubscriptionItemProps> = React.memo(
  ({ subscription, account, onClick, formatCurrency }) => {
    return (
      <button
        onClick={onClick}
        className="w-full text-left bg-gray-900 p-3 rounded-xl border border-gray-800 flex items-center space-x-4 hover:bg-gray-800 transition-colors duration-200"
      >
        <div className="rounded-full p-2 bg-purple-500/10 text-purple-400">
          <RepeatIcon className="w-5 h-5" />
        </div>
        <div className="flex-1">
          <p className="font-semibold text-gray-200">{subscription.description}</p>
          <p className="text-sm text-gray-400">
            {`Next on ${new Date(subscription.startDate).toLocaleDateString()}`}
          </p>
        </div>
        <div className="text-right">
          <p className="font-semibold font-mono text-gray-200">
            {formatCurrency(subscription.amount)}
          </p>
          {account && <p className="text-sm text-gray-500">{account.name}</p>}
        </div>
      </button>
    );
  }
);

SubscriptionItem.displayName = 'SubscriptionItem';
